using UnityEngine;
using System.Collections;

public class Drivevehicleenterexit : MonoBehaviour
{
    public GameObject Enterbutton;
    public GameObject[] Allvehicles;
    public GameObject Exitbutton;
    public float distance = 10f;
    public Transform Player;
    public GameObject Playercamera, vehiclecamera, Playercanvas, vehiclecanvas;
    private GameObject nearvehicle;
    private bool isInVehicle = false;
    public float maxExitSpeed = 5f; // Maximum speed to allow exit (in km/h)
    private Transform originalParent; // Store player's original parent
    public void Start()
    {
        // Find all vehicles with "Vehicle" and "Truck" tags and assign to Allvehicles array
        GameObject[] vehicles = GameObject.FindGameObjectsWithTag("Vehicle");

        // Combine both arrays into Allvehicles
        Allvehicles = new GameObject[vehicles.Length + GameObject.FindGameObjectsWithTag("Truck").Length];
        vehicles.CopyTo(Allvehicles, 0);
        GameObject.FindGameObjectsWithTag("Truck").CopyTo(Allvehicles, vehicles.Length);

        Enterbutton.SetActive(false);
        Exitbutton.SetActive(false);
    }

    public void Update()
    {
        if (!isInVehicle)
        {
            CheckNearVehicles();
        }
        else
        {
            CheckExitCondition();
        }
    }

    private void CheckNearVehicles()
    {
        bool nearVehicleFound = false;
        GameObject closestVehicle = null;
        float closestDistance = float.MaxValue;

        // Check distance to each vehicle in the Allvehicles array
        foreach (GameObject vehicle in Allvehicles)
        {
            if (vehicle != null && Player != null)
            {
                float distanceToVehicle = Vector3.Distance(Player.position, vehicle.transform.position);

                // If vehicle is within range and closer than previous closest
                if (distanceToVehicle <= distance && distanceToVehicle < closestDistance)
                {
                    nearVehicleFound = true;
                    closestVehicle = vehicle;
                    closestDistance = distanceToVehicle;
                }
            }
        }

        // Assign the closest vehicle as nearvehicle
        if (nearVehicleFound)
        {
            nearvehicle = closestVehicle;
        }
        else
        {
            nearvehicle = null; // Clear nearvehicle if no vehicle is near
        }

        // Show/hide enter button based on whether player is near a vehicle
        Enterbutton.SetActive(nearVehicleFound);
    }

    private void CheckExitCondition()
    {
        if (nearvehicle != null)
        {
            // Get vehicle speed in km/h
            float vehicleSpeed = nearvehicle.GetComponent<RCC_CarControllerV4>().speed;

            // Show exit button only if speed is 5 km/h or less
            Exitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
    }


    public void Entervehicle()
    {
        // Store original parent before parenting to vehicle
        originalParent = Player.parent;

        Playercamera.SetActive(false);
        Player.gameObject.SetActive(false);
        vehiclecamera.SetActive(true);
        Playercanvas.SetActive(false);
        vehiclecanvas.SetActive(true);
        nearvehicle.GetComponent<RCC_CarControllerV4>().canControl = true;

        // Parent player to vehicle and set position
        Player.SetParent(nearvehicle.transform);
        Player.position = nearvehicle.GetComponent<VehiclePlayerPosition>().SeatPosition.position;

        nearvehicle.GetComponent<RCC_CarControllerV4>().enabled = true;
        nearvehicle.GetComponent<RCC_CarControllerV4>().engineRunning = true;
        nearvehicle.GetComponent<Rigidbody>().linearDamping = 0.01f;
         nearvehicle.GetComponent<Rigidbody>().isKinematic = false;

        // Hide enter button and set vehicle state
        Enterbutton.SetActive(false);
        isInVehicle = true;
        // Exit button visibility will be controlled by CheckExitCondition based on speed
    }
    public void Exitvehicle()
    {
        // Unparent player from vehicle first
        Player.SetParent(originalParent);

        Playercamera.SetActive(true);
        Player.gameObject.SetActive(true);
        vehiclecamera.SetActive(false);
        Playercanvas.SetActive(true);
        vehiclecanvas.SetActive(false);

        // Set player position to door position (now unparented, so it's relative to world)
        Player.position = nearvehicle.GetComponent<VehiclePlayerPosition>().DoorPosition.position;

        nearvehicle.GetComponent<RCC_CarControllerV4>().canControl = false;
        nearvehicle.GetComponent<RCC_CarControllerV4>().engineRunning = false;
          nearvehicle.GetComponent<Rigidbody>().linearDamping = 10f;
        StartCoroutine(TurnOffEngineAfterDelay());
      

        // Hide exit button and set vehicle state
        Exitbutton.SetActive(false);
        isInVehicle = false;
    }

    private IEnumerator TurnOffEngineAfterDelay()
    {
        yield return new WaitForSeconds(2f); // Wait for 2 seconds
        if (nearvehicle != null)
        {
           
             nearvehicle.GetComponent<RCC_CarControllerV4>().enabled = false;
        }
    }
}
