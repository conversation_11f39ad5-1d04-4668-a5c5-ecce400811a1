using UnityEngine;
using System.Collections;

public class truckrcccam : MonoBehaviour
{
    public RCC_Camera cam;
    public string truckTag = "Truck";
    public float rcccamhight;
    public float rcccamdistance;

    private RCC_CarControllerV4 truckController;
    private bool lastControllerState = false;

    void Start()
    {
        // Find truck once at start instead of every frame
        GameObject truck = GameObject.FindWithTag(truckTag);
        if (truck != null)
        {
            truckController = truck.GetComponent<RCC_CarControllerV4>();
            if (truckController != null)
            {
                // Set initial camera state
                UpdateCameraSettings(truckController.enabled);
                lastControllerState = truckController.enabled;

                // Start coroutine to check state changes efficiently
                StartCoroutine(CheckControllerStateChange());
            }
        }
    }

    // Efficient coroutine that checks state only when needed
    private IEnumerator CheckControllerStateChange()
    {
        while (truckController != null)
        {
            bool currentState = truckController.enabled;

            // Only update camera if state actually changed
            if (currentState != lastControllerState)
            {
                UpdateCameraSettings(currentState);
                lastControllerState = currentState;
            }

            // Check every 0.1 seconds instead of every frame
            yield return new WaitForSeconds(0.1f);
        }
    }

    private void UpdateCameraSettings(bool controllerEnabled)
    {
        if (cam != null)
        {
            if (controllerEnabled)
            {
                // When control is true, use the custom height and distance values
                cam.TPSHeight = rcccamhight;
                cam.TPSDistance = rcccamdistance;
            }
            else
            {
                // When control is false, use default values (13 and 4.7)
                cam.TPSHeight = 4.71f;
                cam.TPSDistance = 13f;
            }
        }
    }

    // Alternative method: You can call this from your vehicle enter/exit scripts
    public void OnTruckControllerStateChanged(bool enabled)
    {
        UpdateCameraSettings(enabled);
    }
}
